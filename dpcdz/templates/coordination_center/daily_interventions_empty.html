{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>الحماية المدنية الجزائرية - التدخلات اليومية المتقدمة</title>
    <!-- نظام CSS الموحد -->
    <link rel="stylesheet" href="{% static 'css/dpc-unified.css' %}">
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'css/home.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">

    <style>
        /* تحسينات CSS للتصميم العصري والنظيف */
        * {
            font-family: 'Cairo', sans-serif !important;
        }

        /* تحسين العنوان الرئيسي */
        .main-title-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
            text-align: center;
            color: white;
        }

        .main-title-section .title-icon {
            font-size: 3rem;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
        }

        .main-title-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .main-title-section .subtitle {
            font-size: 1.1rem;
            margin: 10px 0 0 0;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        /* سطر المعلومات الموحد */
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: end;
            gap: 20px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
        }

        .info-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .info-item label {
            font-weight: 600;
            color: #667eea;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-item label i {
            font-size: 1.1rem;
        }

        .enhanced-select, .enhanced-input {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .enhanced-select:focus, .enhanced-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }

        /* أزرار التنقل المبسطة */
        .navigation-controls-simple {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 25px;
        }

        .navigation-controls-simple .btn {
            border-radius: 8px;
            font-weight: 600;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }

        .navigation-controls-simple .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        /* تحسين الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .info-row {
                flex-direction: column;
                gap: 15px;
            }

            .main-title-section h1 {
                font-size: 2rem;
            }

            .navigation-actions {
                flex-direction: column;
                gap: 10px;
            }

            .navigation-actions .action-btn {
                flex: none;
                padding: 15px;
            }

            .navigation-actions .action-btn i {
                font-size: 1.5rem;
            }

            .navigation-actions .action-btn h3 {
                font-size: 1rem;
            }

            .navigation-controls-simple {
                flex-wrap: wrap;
                justify-content: center;
            }

            .navigation-controls-simple .btn {
                margin: 5px;
                min-width: 120px;
            }
        }

        /* تحسين الأزرار العصرية والجذابة */
        .navigation-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .navigation-actions .action-btn {
            flex: 1;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px 15px;
            margin: 0;
            transition: all 0.3s ease;
            color: #666;
            font-weight: 600;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .navigation-actions .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        /* زر البلاغ الأولي - أحمر */
        .navigation-actions .initial-report-btn {
            border-color: #dc3545;
        }

        .navigation-actions .initial-report-btn:hover {
            background: #dc3545;
            color: white;
        }

        .navigation-actions .initial-report-btn.active {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .navigation-actions .initial-report-btn i {
            color: #dc3545;
        }

        .navigation-actions .initial-report-btn:hover i,
        .navigation-actions .initial-report-btn.active i {
            color: white;
        }

        /* زر عملية التعرف - برتقالي */
        .navigation-actions .reconnaissance-btn {
            border-color: #fd7e14;
        }

        .navigation-actions .reconnaissance-btn:hover {
            background: #fd7e14;
            color: white;
        }

        .navigation-actions .reconnaissance-btn.active {
            background: #fd7e14;
            color: white;
            border-color: #fd7e14;
        }

        .navigation-actions .reconnaissance-btn i {
            color: #fd7e14;
        }

        .navigation-actions .reconnaissance-btn:hover i,
        .navigation-actions .reconnaissance-btn.active i {
            color: white;
        }

        /* زر إنهاء المهمة - أخضر */
        .navigation-actions .completion-btn {
            border-color: #28a745;
        }

        .navigation-actions .completion-btn:hover {
            background: #28a745;
            color: white;
        }

        .navigation-actions .completion-btn.active {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .navigation-actions .completion-btn i {
            color: #28a745;
        }

        .navigation-actions .completion-btn:hover i,
        .navigation-actions .completion-btn.active i {
            color: white;
        }

        /* زر جدول التدخلات - أزرق */
        .navigation-actions .interventions-btn {
            border-color: #007bff;
        }

        .navigation-actions .interventions-btn:hover {
            background: #007bff;
            color: white;
        }

        .navigation-actions .interventions-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .navigation-actions .interventions-btn i {
            color: #007bff;
        }

        .navigation-actions .interventions-btn:hover i,
        .navigation-actions .interventions-btn.active i {
            color: white;
        }

        /* تنسيق الأيقونات والنصوص */
        .navigation-actions .action-btn i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
            transition: all 0.3s ease;
        }

        .navigation-actions .action-btn h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-content-inline {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        /* تحسين النماذج */
        .enhanced-form {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
        }

        .form-section-group {
            margin-bottom: 25px;
        }

        .section-header h5 {
            color: #667eea;
            font-weight: 700;
            font-size: 1.2rem;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }

        .form-control-enhanced {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control-enhanced:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        /* تحسين الجداول */
        .enhanced-table {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .enhanced-table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            padding: 15px;
            border: none;
        }

        .enhanced-table tbody tr {
            transition: all 0.3s ease;
        }

        .enhanced-table tbody tr:hover {
            background-color: #f8f9fa;
            transform: scale(1.01);
        }

        /* تحسين الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .title-row {
                flex-direction: column;
                text-align: center;
            }

            .main-title h1 {
                font-size: 2rem;
            }

            .navigation-actions {
                flex-wrap: wrap;
                justify-content: center;
            }

            .navigation-actions .action-btn {
                margin: 5px;
                min-width: 150px;
            }
        }

        /* تحسين الألوان والظلال */
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            border: none;
            border-radius: 8px;
            font-weight: 600;
        }

        .btn-outline-primary, .btn-outline-info, .btn-outline-success {
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover, .btn-outline-info:hover, .btn-outline-success:hover {
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="flag">
                <img src="{% static 'images/algeria_flag.png' %}" alt="العلم الجزائري">
            </div>
            <div class="title">
                <h1>المديرية العامة للحماية المدنية</h1>
            </div>
            <div class="logo">
                <img src="{% static 'images/civil_protection_logo.png' %}" alt="شعار الحماية المدنية" class="sidebar-toggle" id="sidebar-toggle" style="cursor: pointer;">
            </div>
        </div>
    </header>

    {% include 'includes/sidebar.html' %}

    <main>
        <div class="unified-container">
            {% csrf_token %}

            <!-- العنوان والأدوات المحسن -->
            <div class="header-section">
                <!-- العنوان الرئيسي المحسن -->
                <div class="main-title-section">
                    <div class="title-icon">
                        <i class="fas fa-ambulance"></i>
                    </div>
                    <h1>التدخلات اليومية المتقدمة</h1>
                    <p class="subtitle">مركز التنسيق العملي - إدارة التدخلات من البلاغ الأولي حتى إنهاء المهمة</p>
                </div>

                <!-- معلومات الوحدة والتاريخ في سطر واحد -->
                <div class="info-row">
                    <div class="info-item">
                        <label><i class="fas fa-building"></i> الوحدة</label>
                        <select id="unitSelect" class="form-control enhanced-select" onchange="changeUnit()">
                            {% for unit in units %}
                                <option value="{{ unit.id }}">{{ unit.name }}</option>
                            {% empty %}
                                <option value="">لا توجد وحدات متاحة</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="info-item">
                        <label><i class="fas fa-calendar"></i> التاريخ</label>
                        <input type="date" id="dateSelect" class="form-control enhanced-input"
                               value="{{ today|date:'Y-m-d' }}" onchange="changeDate()">
                    </div>

                    <div class="info-item">
                        <label><i class="fas fa-clock"></i> الفترة الزمنية</label>
                        <select id="timeFilter" class="form-control enhanced-select">
                            <option value="24h">آخر 24 ساعة</option>
                            <option value="12h">آخر 12 ساعة</option>
                            <option value="6h">آخر 6 ساعات</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                    </div>
                </div>

                <!-- أزرار التنقل -->
                <div class="navigation-controls-simple">
                    <a href="{% url 'coordination_center' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right"></i>
                        مركز التنسيق
                    </a>

                    <a href="{% url 'all_interventions' %}" class="btn btn-outline-info">
                        <i class="fas fa-table"></i>
                        صفحة الجداول
                    </a>

                    <button class="btn btn-outline-success" onclick="refreshInterventions()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <!-- الجدول الرئيسي الموحد -->
            <div class="main-table-section">
                <!-- الأزرار الرئيسية للتنقل العصرية -->
                <div class="navigation-actions">
                    <button class="action-btn initial-report-btn active" id="initial-report-btn" onclick="showSection('initial-report')">
                        <div class="btn-content-inline">
                            <i class="fas fa-bullhorn"></i>
                            <h3>بلاغ أولي</h3>
                        </div>
                    </button>

                    <button class="action-btn reconnaissance-btn" id="reconnaissance-btn" onclick="showSection('reconnaissance')">
                        <div class="btn-content-inline">
                            <i class="fas fa-search"></i>
                            <h3>عملية التعرف</h3>
                        </div>
                    </button>

                    <button class="action-btn completion-btn" id="completion-btn" onclick="showSection('completion')">
                        <div class="btn-content-inline">
                            <i class="fas fa-check-circle"></i>
                            <h3>إنهاء المهمة</h3>
                        </div>
                    </button>

                    <button class="action-btn interventions-btn" id="interventions-btn" onclick="showSection('interventions')">
                        <div class="btn-content-inline">
                            <i class="fas fa-list"></i>
                            <h3>جدول التدخلات</h3>
                        </div>
                    </button>
                </div>

                <!-- محتوى التبويبات -->
                <div class="tab-content">
                    <!-- تبويب البلاغ الأولي -->
                    <div class="tab-pane active" id="initial-report-tab">
                        <div class="table-header">
                            <h4><i class="fas fa-bullhorn"></i> البلاغ الأولي</h4>

                        </div>

                        <!-- نموذج البلاغ الأولي المحسن -->
                        <div class="form-section enhanced-form-section" id="initial-report-form" style="display: none;">
                            <div class="form-header-enhanced">
                                <div class="form-title-section">
                                    <div class="form-icon">
                                        <i class="fas fa-bullhorn"></i>
                                    </div>
                                    <div class="form-title-text">
                                        <h4>نموذج البلاغ الأولي</h4>
                                        <p>تسجيل بلاغ تدخل جديد من أي جهة متصلة</p>
                                    </div>
                                </div>
                                <button class="btn-close-form" onclick="hideInitialReportForm()" title="إغلاق النموذج">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <form id="initialReportForm" class="enhanced-form">
                                <!-- القسم الأول: معلومات التدخل الأساسية -->
                                <div class="form-section-group">
                                    <div class="section-header">
                                        <h5><i class="fas fa-info-circle"></i> معلومات التدخل الأساسية</h5>
                                    </div>
                                    <div class="form-grid-enhanced">
                                        <div class="form-group">
                                            <label for="exitTime" class="form-label-enhanced">
                                                <i class="fas fa-clock"></i>
                                                ساعة ودقيقة الخروج *
                                            </label>
                                            <input type="time" class="form-control-enhanced" id="exitTime" name="exit_time" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="interventionLocation" class="form-label-enhanced">
                                                <i class="fas fa-map-marker-alt"></i>
                                                مكان التدخل *
                                            </label>
                                            <input type="text" class="form-control-enhanced" id="interventionLocation" name="intervention_location" required placeholder="أدخل مكان التدخل">
                                        </div>

                                        <div class="form-group">
                                            <label for="interventionType" class="form-label-enhanced">
                                                <i class="fas fa-list-alt"></i>
                                                نوع التدخل *
                                            </label>
                                            <select class="form-control-enhanced" id="interventionType" name="intervention_type" required>
                                                <option value="">اختر نوع التدخل</option>
                                                <option value="medical">🚑 إجلاء صحي</option>
                                                <option value="accident">🚗 حادث مرور</option>
                                                <option value="agricultural-fire">🔥 حريق محاصيل زراعية</option>
                                                <option value="building-fire">🏢 حرائق البنايات والمؤسسات</option>
                                                <option value="other">⚙️ عمليات مختلفة</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- القسم الثاني: معلومات الاتصال -->
                                <div class="form-section-group">
                                    <div class="section-header">
                                        <h5><i class="fas fa-phone"></i> معلومات الاتصال</h5>
                                    </div>
                                    <div class="form-grid-enhanced">
                                        <div class="form-group">
                                            <label for="contactSource" class="form-label-enhanced">
                                                <i class="fas fa-users"></i>
                                                الجهة المتصلة *
                                            </label>
                                            <select class="form-control-enhanced" id="contactSource" name="contact_source" required>
                                                <option value="">اختر الجهة المتصلة</option>
                                                <option value="citizen">👤 مواطن</option>
                                                <option value="police">👮 الشرطة</option>
                                                <option value="gendarmerie">🛡️ الدرك الوطني</option>
                                                <option value="army">🎖️ الجيش الوطني الشعبي</option>
                                                <option value="forest">🌲 مصالح الغابات</option>
                                                <option value="customs">🛃 الجمارك</option>
                                                <option value="local-authorities">🏛️ السلطات المحلية</option>
                                                <option value="other">❓ أخرى</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="contactType" class="form-label-enhanced">
                                                <i class="fas fa-phone-alt"></i>
                                                نوع الاتصال *
                                            </label>
                                            <select class="form-control-enhanced" id="contactType" name="contact_type" required>
                                                <option value="">اختر نوع الاتصال</option>
                                                <option value="phone">📞 هاتف</option>
                                                <option value="radio">📻 راديو</option>
                                                <option value="unit-request">🚨 وحدة تطلب الدعم</option>
                                                <option value="direct">👥 مباشر</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="phoneNumber" class="form-label-enhanced">
                                                <i class="fas fa-hashtag"></i>
                                                رقم الهاتف
                                            </label>
                                            <input type="tel" class="form-control-enhanced" id="phoneNumber" name="phone_number" placeholder="أدخل رقم الهاتف">
                                        </div>
                                    </div>
                                </div>

                                <!-- القسم الثالث: الوسائل المرسلة -->
                                <div class="form-section-group">
                                    <div class="section-header">
                                        <h5><i class="fas fa-truck"></i> الوسائل المرسلة</h5>
                                    </div>
                                    <div class="vehicles-section">
                                        <div id="vehiclesList" class="vehicles-container-enhanced">
                                            <div class="loading-vehicles">
                                                <i class="fas fa-spinner fa-spin"></i>
                                                جاري تحميل الوسائل المتاحة...
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- القسم الرابع: ملاحظات إضافية -->
                                <div class="form-section-group">
                                    <div class="section-header">
                                        <h5><i class="fas fa-sticky-note"></i> ملاحظات إضافية</h5>
                                    </div>
                                    <div class="notes-section">
                                        <textarea class="form-control-enhanced textarea-enhanced" id="additionalNotes" name="additional_notes" rows="4" placeholder="أدخل أي ملاحظات إضافية حول التدخل..."></textarea>
                                    </div>
                                </div>

                                <!-- أزرار الإجراءات -->
                                <div class="form-actions-enhanced">
                                    <button type="button" class="btn-cancel" onclick="hideInitialReportForm()">
                                        <i class="fas fa-times"></i>
                                        إلغاء
                                    </button>
                                    <button type="button" class="btn-submit" onclick="submitInitialReport()">
                                        <i class="fas fa-save"></i>
                                        حفظ البلاغ
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- جدول البلاغات الأولية المحسن -->
                        <div class="table-container-enhanced">
                            <div class="table-header-enhanced">
                                <h5>
                                    <i class="fas fa-list"></i>
                                    البلاغات الأولية المسجلة
                                </h5>
                                <div class="table-stats">
                                    <span class="stat-badge">
                                        <i class="fas fa-clipboard-list"></i>
                                        <span id="totalReports">0</span> بلاغ
                                    </span>
                                </div>
                            </div>

                            <div class="table-wrapper-enhanced">
                                <table class="enhanced-table">
                                    <thead>
                                        <tr>
                                            <th><i class="fas fa-hashtag"></i> رقم</th>
                                            <th><i class="fas fa-clock"></i> وقت الخروج</th>
                                            <th><i class="fas fa-list-alt"></i> نوع التدخل</th>
                                            <th><i class="fas fa-map-marker-alt"></i> مكان التدخل</th>
                                            <th><i class="fas fa-truck"></i> الوسيلة</th>
                                            <th><i class="fas fa-users"></i> الجهة المتصلة</th>
                                            <th><i class="fas fa-info-circle"></i> الحالة</th>
                                            <th><i class="fas fa-cogs"></i> إجراء</th>
                                        </tr>
                                    </thead>
                                    <tbody id="initialReportsTableBody">
                                        <tr class="empty-row">
                                            <td colspan="8" class="empty-message">
                                                <div class="empty-state-enhanced">
                                                    <i class="fas fa-clipboard-list"></i>
                                                    <h6>لا توجد بلاغات مسجلة</h6>
                                                    <p>ابدأ بإضافة بلاغ أولي جديد</p>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب عملية التعرف -->
                    <div class="tab-pane" id="reconnaissance-tab">
                        <div class="table-header">
                            <h4><i class="fas fa-search"></i> عملية التعرف</h4>
                            <div class="table-controls-enhanced">
                                <div class="filters-container">
                                    <div class="filter-group">
                                        <label class="filter-label">
                                            <i class="fas fa-filter"></i>
                                            نوع التدخل
                                        </label>
                                        <select id="reconnaissanceTypeFilter" class="form-control filter-select">
                                            <option value="">جميع الأنواع</option>
                                            <option value="medical">إجلاء صحي</option>
                                            <option value="accident">حادث مرور</option>
                                            <option value="agricultural-fire">حريق محاصيل زراعية</option>
                                            <option value="building-fire">حرائق البنايات والمؤسسات</option>
                                            <option value="other">عمليات مختلفة</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="table-container">
                            <table class="enhanced-table">
                                <thead>
                                    <tr>
                                        <th>رقم</th>
                                        <th>وقت الخروج</th>
                                        <th>نوع التدخل</th>
                                        <th>مكان التدخل</th>
                                        <th>الوسيلة</th>
                                        <th>الحالة</th>
                                        <th>إجراء</th>
                                    </tr>
                                </thead>
                                <tbody id="reconnaissanceTableBody">
                                    <!-- التدخلات التي تحتاج تعرف -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تبويب إنهاء المهمة -->
                    <div class="tab-pane" id="completion-tab">
                        <div class="table-header">
                            <h4><i class="fas fa-check-circle"></i> إنهاء المهمة</h4>
                            <div class="table-controls-enhanced">
                                <div class="filters-container">
                                    <div class="filter-group">
                                        <label class="filter-label">
                                            <i class="fas fa-filter"></i>
                                            نوع التدخل
                                        </label>
                                        <select id="completionTypeFilter" class="form-control filter-select">
                                            <option value="">جميع الأنواع</option>
                                            <option value="medical">إجلاء صحي</option>
                                            <option value="accident">حادث مرور</option>
                                            <option value="agricultural-fire">حريق محاصيل زراعية</option>
                                            <option value="building-fire">حرائق البنايات والمؤسسات</option>
                                            <option value="other">عمليات مختلفة</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="table-container">
                            <table class="enhanced-table">
                                <thead>
                                    <tr>
                                        <th>رقم</th>
                                        <th>وقت الخروج</th>
                                        <th>نوع التدخل</th>
                                        <th>مكان التدخل</th>
                                        <th>الوسيلة</th>
                                        <th>الحالة</th>
                                        <th>إجراء</th>
                                    </tr>
                                </thead>
                                <tbody id="completionTableBody">
                                    <!-- التدخلات التي تحتاج إنهاء -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تبويب جدول التدخلات -->
                    <div class="tab-pane" id="interventions-tab">
                        <div class="table-header">
                            <h4><i class="fas fa-list"></i> جدول التدخلات اليومية</h4>
                            <div class="table-controls-enhanced">
                                <div class="filters-container">
                                    <div class="filter-group">
                                        <label class="filter-label">
                                            <i class="fas fa-filter"></i>
                                            الحالة
                                        </label>
                                        <select id="statusFilter" class="form-control filter-select">
                                            <option value="">جميع الحالات</option>
                                            <option value="initial_report">بلاغ أولي</option>
                                            <option value="reconnaissance">قيد التعرف</option>
                                            <option value="intervention">عملية تدخل</option>
                                            <option value="completed">منتهية</option>
                                            <option value="escalated">كارثة كبرى</option>
                                        </select>
                                    </div>

                                    <div class="filter-group">
                                        <label class="filter-label">
                                            <i class="fas fa-search"></i>
                                            البحث
                                        </label>
                                        <input type="text" id="interventionSearch" class="form-control filter-input"
                                               placeholder="ابحث في التدخلات...">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="table-container">
                            <table class="enhanced-table">
                                <thead>
                                    <tr>
                                        <th>رقم</th>
                                        <th>وقت التدخل</th>
                                        <th>نوع التدخل</th>
                                        <th>مكان التدخل</th>
                                        <th>الوحدة</th>
                                        <th>الوسيلة</th>
                                        <th>الحالة</th>
                                        <th>إجراء</th>
                                    </tr>
                                </thead>
                                <tbody id="interventionsTableBody">
                                    <!-- جميع التدخلات -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <p>حقوق النشر © 2025 جميع الحقوق محفوظة - تم التطوير بواسطة عبد الرزاق مختاري</p>
    </footer>

    <!-- CSS مخصص لتحسين التصميم -->
    <style>
        /* تحسينات النموذج */
        .enhanced-form-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin: 25px 0;
            overflow: hidden;
            border: 1px solid #e3f2fd;
        }

        .form-header-enhanced {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            color: white;
            padding: 20px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 3px solid #b71c1c;
        }

        .form-title-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .form-icon {
            background: rgba(255, 255, 255, 0.2);
            padding: 12px;
            border-radius: 50%;
            font-size: 1.2rem;
        }

        .form-title-text h4 {
            margin: 0;
            font-size: 1.4rem;
            font-weight: 700;
        }

        .form-title-text p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .btn-close-form {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .btn-close-form:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* أقسام النموذج */
        .form-section-group {
            margin: 25px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .section-header {
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #f0f0f0;
        }

        .section-header h5 {
            color: #1976d2;
            font-weight: 600;
            margin: 0;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-header i {
            color: #1976d2;
            font-size: 1rem;
        }

        /* شبكة النموذج المحسنة */
        .form-grid-enhanced {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label-enhanced {
            color: #424242;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-label-enhanced i {
            color: #1976d2;
            font-size: 0.9rem;
        }

        .form-control-enhanced {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .form-control-enhanced:focus {
            border-color: #1976d2;
            background: white;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
            outline: none;
        }

        .form-control-enhanced:hover {
            border-color: #1976d2;
            background: white;
        }

        /* قسم الوسائل */
        .vehicles-section {
            margin-top: 15px;
        }

        .vehicles-container-enhanced {
            background: #f8f9fa;
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            min-height: 120px;
            transition: all 0.3s ease;
        }

        .vehicles-container-enhanced:hover {
            border-color: #1976d2;
            background: #f3f8ff;
        }

        .loading-vehicles {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .loading-vehicles i {
            margin-left: 8px;
            color: #1976d2;
        }

        /* شبكة الوسائل */
        .vehicles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .vehicle-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .vehicle-card.available {
            border-color: #4caf50;
        }

        .vehicle-card.unavailable {
            border-color: #f44336;
            opacity: 0.6;
            cursor: not-allowed;
        }

        .vehicle-card:hover:not(.unavailable) {
            border-color: #1976d2;
            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
            transform: translateY(-2px);
        }

        .vehicle-checkbox {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .vehicle-label {
            cursor: pointer;
            display: block;
            margin: 0;
        }

        .vehicle-info {
            padding-right: 35px;
        }

        .vehicle-name {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1.1rem;
            margin-bottom: 8px;
            color: #333;
        }

        .vehicle-name i {
            color: #1976d2;
        }

        .vehicle-type {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .vehicle-status {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.85rem;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 15px;
            width: fit-content;
        }

        .status-available {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-unavailable {
            background: #ffebee;
            color: #c62828;
        }

        /* رسالة عدم وجود وسائل */
        .no-vehicles-message {
            text-align: center;
            padding: 30px;
            color: #666;
        }

        .no-vehicles-message i {
            font-size: 2rem;
            color: #ff9800;
            margin-bottom: 10px;
        }

        .no-vehicles-message p {
            margin: 10px 0 5px 0;
            font-weight: 600;
            color: #333;
        }

        .no-vehicles-message small {
            color: #999;
        }

        /* منطقة النص */
        .notes-section {
            margin-top: 15px;
        }

        .textarea-enhanced {
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
        }

        /* أزرار الإجراءات */
        .form-actions-enhanced {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin: 25px;
            padding-top: 20px;
            border-top: 2px solid #f0f0f0;
        }

        .btn-cancel {
            background: linear-gradient(135deg, #757575 0%, #616161 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-cancel:hover {
            background: linear-gradient(135deg, #616161 0%, #424242 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .btn-submit {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-submit:hover {
            background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        /* تحسينات الجدول المحسن */
        .table-container-enhanced {
            margin-top: 30px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid #e3f2fd;
        }

        .table-header-enhanced {
            background: white;
            color: #333;
            padding: 20px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 3px solid #f0f0f0;
        }

        .table-header-enhanced h5 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .table-stats {
            display: flex;
            gap: 15px;
        }

        .stat-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .table-wrapper-enhanced {
            overflow-x: auto;
        }

        .enhanced-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }

        .enhanced-table thead {
            background: white;
            color: #333;
        }

        .enhanced-table th {
            padding: 18px 15px;
            text-align: center;
            font-weight: 600;
            font-size: 0.95rem;
            border-bottom: 2px solid #f0f0f0;
            white-space: nowrap;
        }

        .enhanced-table th i {
            margin-left: 5px;
            opacity: 0.8;
        }

        /* ألوان رؤوس الأعمدة حسب نوع النموذج */
        #initial-report-tab .enhanced-table thead {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            color: white;
        }

        #initial-report-tab .enhanced-table th {
            border-bottom: 2px solid #b71c1c;
        }

        #reconnaissance-tab .enhanced-table thead {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
        }

        #reconnaissance-tab .enhanced-table th {
            border-bottom: 2px solid #e65100;
        }

        #completion-tab .enhanced-table thead {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            color: white;
        }

        #completion-tab .enhanced-table th {
            border-bottom: 2px solid #1b5e20;
        }

        #interventions-tab .enhanced-table thead {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
        }

        #interventions-tab .enhanced-table th {
            border-bottom: 2px solid #0d47a1;
        }

        /* ألوان headers النماذج حسب النوع */
        #initial-report-tab .form-header-enhanced {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            border-bottom: 3px solid #b71c1c;
        }

        #reconnaissance-tab .form-header-enhanced {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            border-bottom: 3px solid #e65100;
        }

        #completion-tab .form-header-enhanced {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            border-bottom: 3px solid #1b5e20;
        }

        #interventions-tab .form-header-enhanced {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            border-bottom: 3px solid #0d47a1;
        }

        .enhanced-table td {
            padding: 15px 12px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
            transition: all 0.3s ease;
            vertical-align: middle;
        }

        .enhanced-table tbody tr:hover:not(.empty-row) {
            background-color: #f3f8ff;
            transform: scale(1.01);
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
        }

        .enhanced-table tbody tr:nth-child(even):not(.empty-row) {
            background-color: #fafafa;
        }

        /* حالة الجدول الفارغ */
        .empty-row {
            background: transparent !important;
        }

        .empty-message {
            padding: 0 !important;
            border: none !important;
        }

        .empty-state-enhanced {
            padding: 60px 20px;
            text-align: center;
            color: #666;
        }

        .empty-state-enhanced i {
            font-size: 3rem;
            color: #bbb;
            margin-bottom: 15px;
            opacity: 0.7;
        }

        .empty-state-enhanced h6 {
            margin: 15px 0 8px 0;
            color: #333;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .empty-state-enhanced p {
            margin: 0;
            color: #999;
            font-size: 0.95rem;
        }

        /* تحسينات أزرار التنقل */
        .navigation-actions .action-btn {
            transition: all 0.3s ease;
        }

        .navigation-actions .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        /* ألوان الأزرار حسب النوع */
        .initial-report-btn.active {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%) !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3) !important;
        }

        .reconnaissance-btn.active {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;
        }

        .completion-btn.active {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%) !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3) !important;
        }

        .interventions-btn.active {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%) !important;
            color: white !important;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3) !important;
        }

        /* تحسينات عامة */
        .table-header h4 {
            color: #1976d2;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .table-header h4 i {
            color: #1976d2;
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .form-grid-enhanced {
                grid-template-columns: 1fr;
            }

            .form-actions-enhanced {
                flex-direction: column;
            }

            .btn-cancel, .btn-submit {
                width: 100%;
                justify-content: center;
            }
        }
    </style>

    <!-- JavaScript -->
    <script src="{% static 'js/sidebar.js' %}"></script>
    <script>
        // Global variables
        let currentSection = 'initial-report';
        let currentInterventions = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚨 صفحة التدخلات اليومية المتقدمة - تم التحميل');

            // Set current time as default
            const now = new Date();
            const timeString = now.toTimeString().slice(0, 5);
            const exitTimeInput = document.getElementById('exitTime');
            if (exitTimeInput) {
                exitTimeInput.value = timeString;
            }

            loadInterventions();
            setupFilters();
        });

        // Show section function (نفس unified-morning-check)
        function showSection(section) {
            console.log('📋 تبديل القسم إلى:', section);

            // Update active button
            document.querySelectorAll('.action-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(section + '-btn').classList.add('active');

            // Update active tab
            document.querySelectorAll('.tab-pane').forEach(tab => tab.classList.remove('active'));
            document.getElementById(section + '-tab').classList.add('active');

            currentSection = section;

            // إظهار النموذج تلقائياً عند النقر على البلاغ الأولي
            if (section === 'initial-report') {
                setTimeout(() => {
                    showInitialReportForm();
                }, 100);
            }

            // Load data for specific section
            if (section === 'reconnaissance') {
                loadReconnaissanceData();
            } else if (section === 'completion') {
                loadCompletionData();
            } else if (section === 'interventions') {
                loadAllInterventions();
            }
        }

        // Show/Hide initial report form
        function showInitialReportForm() {
            const form = document.getElementById('initial-report-form');
            if (form) {
                form.style.display = 'block';
                loadAvailableVehicles();
            }
        }

        function hideInitialReportForm() {
            const form = document.getElementById('initial-report-form');
            if (form) {
                form.style.display = 'none';
                // Reset form
                const formElement = document.getElementById('initialReportForm');
                if (formElement) {
                    formElement.reset();
                }
            }
        }

        // Load available vehicles
        function loadAvailableVehicles() {
            const vehiclesList = document.getElementById('vehiclesList');
            if (!vehiclesList) return;

            vehiclesList.innerHTML = '<div class="text-muted">جاري تحميل الوسائل المتاحة...</div>';

            fetch('/api/get-available-vehicles/', {
                method: 'GET',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.vehicles) {
                    let vehiclesHtml = '';
                    data.vehicles.forEach(vehicle => {
                        const isDisabled = !vehicle.available ? 'disabled' : '';
                        const statusClass = vehicle.available ? 'text-success' : 'text-danger';

                        vehiclesHtml += `
                            <div class="vehicle-card ${vehicle.available ? 'available' : 'unavailable'}" ${isDisabled ? 'data-disabled="true"' : ''}>
                                <input class="vehicle-checkbox" type="checkbox" value="${vehicle.id}"
                                       id="vehicle_${vehicle.id}" name="assigned_vehicles" ${isDisabled}>
                                <label class="vehicle-label" for="vehicle_${vehicle.id}">
                                    <div class="vehicle-info">
                                        <div class="vehicle-name">
                                            <i class="fas fa-truck"></i>
                                            <strong>${vehicle.name}</strong>
                                        </div>
                                        <div class="vehicle-type">${vehicle.type}</div>
                                        <div class="vehicle-status ${vehicle.available ? 'status-available' : 'status-unavailable'}">
                                            <i class="fas ${vehicle.available ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                            ${vehicle.status}
                                        </div>
                                    </div>
                                </label>
                            </div>
                        `;
                    });

                    if (vehiclesHtml === '') {
                        vehiclesHtml = `
                            <div class="no-vehicles-message">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>لا توجد وسائل متاحة حالياً</p>
                                <small>يرجى التحقق من حالة الوسائل أو المحاولة لاحقاً</small>
                            </div>
                        `;
                    }

                    vehiclesList.innerHTML = `
                        <div class="vehicles-grid">
                            ${vehiclesHtml}
                        </div>
                    `;
                } else {
                    vehiclesList.innerHTML = '<div class="text-danger">خطأ في تحميل الوسائل</div>';
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل الوسائل:', error);
                vehiclesList.innerHTML = '<div class="text-danger">خطأ في الاتصال بالخادم</div>';
            });
        }

        // Submit initial report
        function submitInitialReport() {
            const form = document.getElementById('initialReportForm');
            if (!form) return;

            const formData = new FormData(form);

            // Get selected vehicles
            const selectedVehicles = [];
            const vehicleCheckboxes = document.querySelectorAll('input[name="assigned_vehicles"]:checked');
            vehicleCheckboxes.forEach(checkbox => {
                selectedVehicles.push(checkbox.value);
            });

            if (selectedVehicles.length === 0) {
                showNotification('يجب اختيار وسيلة واحدة على الأقل', 'warning');
                return;
            }

            // Validate required fields
            const requiredFields = ['exit_time', 'intervention_location', 'intervention_type', 'contact_source', 'contact_type'];
            let isValid = true;

            requiredFields.forEach(field => {
                const input = document.getElementById(field.replace('_', ''));
                if (!input || !input.value.trim()) {
                    isValid = false;
                    if (input) {
                        input.classList.add('is-invalid');
                    }
                } else {
                    if (input) {
                        input.classList.remove('is-invalid');
                    }
                }
            });

            if (!isValid) {
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            // Prepare data for submission
            const reportData = {
                exit_time: formData.get('exit_time'),
                intervention_location: formData.get('intervention_location'),
                intervention_type: formData.get('intervention_type'),
                contact_source: formData.get('contact_source'),
                contact_type: formData.get('contact_type'),
                phone_number: formData.get('phone_number'),
                additional_notes: formData.get('additional_notes'),
                assigned_vehicles: selectedVehicles
            };

            console.log('📊 بيانات البلاغ الأولي:', reportData);

            // Submit to Django backend
            fetch('/api/create-initial-report/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify(reportData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(`تم حفظ البلاغ الأولي بنجاح! رقم التدخل: ${data.intervention_number}`, 'success');

                    // Hide form and refresh table
                    hideInitialReportForm();

                    // Refresh interventions table
                    setTimeout(() => {
                        loadInterventions();
                    }, 1000);
                } else {
                    showNotification(data.error || 'حدث خطأ في حفظ البلاغ', 'danger');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showNotification('حدث خطأ في الاتصال بالخادم', 'danger');
            });
        }

        // Load data for different sections
        function loadReconnaissanceData() {
            console.log('🧭 تحميل بيانات عملية التعرف');
            // Load interventions that need reconnaissance
        }

        function loadCompletionData() {
            console.log('✅ تحميل بيانات إنهاء المهمة');
            // Load interventions that need completion
        }

        function loadAllInterventions() {
            console.log('📋 تحميل جميع التدخلات');
            // Load all interventions
        }

        // Load interventions from server
        function loadInterventions() {
            console.log('📊 تحميل التدخلات...');
            // This will be connected to Django backend later
            updateInterventionsTable();
        }

        // Setup filter functionality
        function setupFilters() {
            const statusFilter = document.getElementById('statusFilter');
            const reconnaissanceTypeFilter = document.getElementById('reconnaissanceTypeFilter');
            const completionTypeFilter = document.getElementById('completionTypeFilter');
            const interventionSearch = document.getElementById('interventionSearch');

            if (statusFilter) {
                statusFilter.addEventListener('change', filterInterventions);
            }
            if (reconnaissanceTypeFilter) {
                reconnaissanceTypeFilter.addEventListener('change', filterInterventions);
            }
            if (completionTypeFilter) {
                completionTypeFilter.addEventListener('change', filterInterventions);
            }
            if (interventionSearch) {
                interventionSearch.addEventListener('input', filterInterventions);
            }
        }

        // Filter interventions
        function filterInterventions() {
            console.log('🔍 تطبيق الفلاتر');
            // Filter logic will be implemented
        }

        // Update interventions table
        function updateInterventionsTable() {
            console.log('🔄 تحديث جدول التدخلات');
            // This function will be expanded when connected to backend
        }

        // Control functions
        function changeUnit() {
            const unitSelect = document.getElementById('unitSelect');
            if (unitSelect) {
                console.log('🏢 تغيير الوحدة إلى:', unitSelect.value);
                loadInterventions();
            }
        }

        function changeDate() {
            const dateSelect = document.getElementById('dateSelect');
            if (dateSelect) {
                console.log('📅 تغيير التاريخ إلى:', dateSelect.value);
                loadInterventions();
            }
        }

        function refreshInterventions() {
            console.log('🔄 تحديث التدخلات');
            loadInterventions();
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            `;
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Auto-refresh functionality
        setInterval(function() {
            if (currentSection === 'interventions') {
                loadInterventions();
            }
        }, 30000); // Refresh every 30 seconds
    </script>
</body>
</html>
